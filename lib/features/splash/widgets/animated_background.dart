import 'dart:math';
import 'package:flutter/material.dart';
import 'package:pixs/shared/constants/colors.dart';

class AnimatedBackground extends StatelessWidget {
  final double animationValue;
  final Widget child;

  const AnimatedBackground({
    super.key,
    required this.animationValue,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.topLeft,
          radius: 1.5 + (sin(animationValue * 2 * pi) * 0.2),
          colors: [
            kSecondaryColor.withValues(alpha: 0.1),
            kBackgroundColor,
            kPrimaryColor.withValues(alpha: 0.8),
            kBackgroundColor,
          ],
          stops: [
            0.0,
            0.3 + (cos(animationValue * pi) * 0.1),
            0.7 + (sin(animationValue * pi) * 0.1),
            1.0,
          ],
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              kBackgroundColor.withValues(alpha: 0.1),
              Colors.transparent,
            ],
            stops: [
              0.0,
              0.5 + (sin(animationValue * 3 * pi) * 0.2),
              1.0,
            ],
          ),
        ),
        child: child,
      ),
    );
  }
}

class FloatingElements extends StatelessWidget {
  final double animationValue;

  const FloatingElements({
    super.key,
    required this.animationValue,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned.fill(
      child: CustomPaint(
        painter: FloatingElementsPainter(animationValue),
      ),
    );
  }
}

class FloatingElementsPainter extends CustomPainter {
  final double animationValue;

  FloatingElementsPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    // Draw floating geometric shapes
    _drawFloatingShapes(canvas, size);
    
    // Draw subtle light rays
    _drawLightRays(canvas, size);
  }

  void _drawFloatingShapes(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.fill
      ..color = kSecondaryColor.withValues(alpha: 0.05);

    // Draw floating rectangles
    for (int i = 0; i < 6; i++) {
      final x = (size.width * (i / 6)) + 
          (sin(animationValue * 2 * pi + i) * 40);
      final y = (size.height * ((i * 0.4) % 1)) + 
          (cos(animationValue * 1.5 * pi + i) * 30);
      
      final rect = Rect.fromCenter(
        center: Offset(x, y),
        width: 20 + (sin(animationValue * 4 * pi + i) * 5),
        height: 20 + (cos(animationValue * 4 * pi + i) * 5),
      );
      
      canvas.save();
      canvas.translate(rect.center.dx, rect.center.dy);
      canvas.rotate(animationValue * 2 * pi + i);
      canvas.translate(-rect.center.dx, -rect.center.dy);
      canvas.drawRRect(
        RRect.fromRectAndRadius(rect, const Radius.circular(4)),
        paint,
      );
      canvas.restore();
    }

    // Draw floating circles
    final circlePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..color = kSecondaryColor.withValues(alpha: 0.1);

    for (int i = 0; i < 4; i++) {
      final x = (size.width * (i / 4)) + 
          (sin(animationValue * pi + i * 0.5) * 60);
      final y = (size.height * ((i * 0.6) % 1)) + 
          (cos(animationValue * pi + i * 0.5) * 40);
      
      final radius = 15 + (sin(animationValue * 3 * pi + i) * 8);
      
      canvas.drawCircle(
        Offset(x, y),
        radius,
        circlePaint,
      );
    }
  }

  void _drawLightRays(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5
      ..color = kSecondaryColor.withValues(alpha: 0.08);

    // Draw diagonal light rays
    for (int i = 0; i < 8; i++) {
      final startX = -50.0 + (i * (size.width + 100) / 8);
      final endX = startX + 100;
      final offset = sin(animationValue * 2 * pi + i) * 20;
      
      canvas.drawLine(
        Offset(startX + offset, -50),
        Offset(endX + offset, size.height + 50),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
