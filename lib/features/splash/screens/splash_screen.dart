import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:pixs/features/home/<USER>/home/<USER>';
import 'package:pixs/features/onboarding/services/onboarding_service.dart';
import 'package:pixs/shared/app/extension/helper.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/constants/images.dart';
import 'package:pixs/shared/dependency_injection/injectable.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/themes/font_palette.dart';
import 'package:pixs/features/splash/widgets/animated_background.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _logoController;
  late AnimationController _textController;
  late AnimationController _particleController;

  late Animation<double> _backgroundAnimation;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoOpacityAnimation;
  late Animation<Offset> _logoSlideAnimation;
  late Animation<double> _textOpacityAnimation;
  late Animation<Offset> _textSlideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    Helper.afterInit(_initialFunction);
    _startAnimationSequence();
  }

  void _initializeAnimations() {
    // Background animation controller
    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Logo animation controller
    _logoController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    // Text animation controller
    _textController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Particle animation controller
    _particleController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );

    // Background animations
    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundController,
      curve: Curves.easeInOut,
    ));

    // Logo animations
    _logoScaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _logoOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeIn),
    ));

    _logoSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.5),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.easeOutBack,
    ));

    // Text animations
    _textOpacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeIn,
    ));

    _textSlideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _textController,
      curve: Curves.easeOutCubic,
    ));
  }

  void _initialFunction() {
    final currentContext = context;
    context.read<HomeCubit>().getImages();
    Future.delayed(const Duration(seconds: 4), () async {
      if (!currentContext.mounted) return;

      // Check if this is the first time user
      final onboardingService = getIt<OnboardingService>();
      final isFirstTime = onboardingService.isFirstTime;

      if (isFirstTime) {
        Navigator.pushNamedAndRemoveUntil(
            currentContext, routeOnboarding, ModalRoute.withName(routeRoot));
      } else {
        Navigator.pushNamedAndRemoveUntil(
            currentContext, routeMain, ModalRoute.withName(routeRoot));
      }
    });
  }

  void _startAnimationSequence() {
    // Start background animation immediately
    _backgroundController.forward();

    // Start particle animation
    _particleController.repeat();

    // Start logo animation after a short delay
    Future.delayed(const Duration(milliseconds: 300), () {
      if (mounted) _logoController.forward();
    });

    // Start text animation after logo animation begins
    Future.delayed(const Duration(milliseconds: 800), () {
      if (mounted) _textController.forward();
    });
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _logoController.dispose();
    _textController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: AnimatedBuilder(
        animation: Listenable.merge([
          _backgroundController,
          _logoController,
          _textController,
          _particleController,
        ]),
        builder: (context, child) {
          return AnimatedBackground(
            animationValue: _backgroundAnimation.value,
            child: Stack(
              children: [
                // Floating elements background
                FloatingElements(animationValue: _particleController.value),

                // Animated particles background
                _buildParticlesBackground(),

                // Main content
                Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Logo with animations
                      SlideTransition(
                        position: _logoSlideAnimation,
                        child: FadeTransition(
                          opacity: _logoOpacityAnimation,
                          child: ScaleTransition(
                            scale: _logoScaleAnimation,
                            child: Hero(
                              tag: 'logo',
                              child: Container(
                                padding: EdgeInsets.all(20.w),
                                decoration: BoxDecoration(
                                  shape: BoxShape.circle,
                                  boxShadow: [
                                    BoxShadow(
                                      color: kSecondaryColor.withValues(alpha: 0.3),
                                      blurRadius: 30,
                                      spreadRadius: 5,
                                    ),
                                  ],
                                ),
                                child: Image.asset(
                                  Assets.kLogo,
                                  width: 120.w,
                                  height: 120.w,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: 40.h),

                      // App name with animation
                      SlideTransition(
                        position: _textSlideAnimation,
                        child: FadeTransition(
                          opacity: _textOpacityAnimation,
                          child: Text(
                            'PIXS',
                            style: FontPalette.urbenist42.copyWith(
                              color: kWhite,
                              fontWeight: FontWeight.bold,
                              letterSpacing: 4.0,
                              shadows: [
                                Shadow(
                                  color: kSecondaryColor.withValues(alpha: 0.5),
                                  offset: const Offset(0, 2),
                                  blurRadius: 8,
                                ),
                              ],
                            ),
                          ).animate().shimmer(
                            duration: 2000.ms,
                            color: kSecondaryColor.withValues(alpha: 0.3),
                          ),
                        ),
                      ),

                      SizedBox(height: 16.h),

                      // Tagline with animation
                      SlideTransition(
                        position: _textSlideAnimation,
                        child: FadeTransition(
                          opacity: _textOpacityAnimation,
                          child: Text(
                            'Discover Beautiful Photos',
                            style: FontPalette.urbenist18.copyWith(
                              color: kWhite.withValues(alpha: 0.8),
                              fontWeight: FontWeight.w300,
                              letterSpacing: 1.0,
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: 8.h),

                      // Subtitle with animation
                      SlideTransition(
                        position: _textSlideAnimation,
                        child: FadeTransition(
                          opacity: _textOpacityAnimation,
                          child: Text(
                            'High-quality images for every project',
                            style: FontPalette.urbenist14.copyWith(
                              color: kSecondaryColor.withValues(alpha: 0.9),
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: 60.h),

                      // Loading indicator
                      FadeTransition(
                        opacity: _textOpacityAnimation,
                        child: _buildLoadingIndicator(),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildParticlesBackground() {
    return Positioned.fill(
      child: CustomPaint(
        painter: ParticlesPainter(_particleController.value),
      ),
    );
  }

  Widget _buildLoadingIndicator() {
    return Column(
      children: [
        SizedBox(
          width: 40.w,
          height: 40.w,
          child: CircularProgressIndicator(
            strokeWidth: 2.0,
            valueColor: AlwaysStoppedAnimation<Color>(
              kSecondaryColor.withValues(alpha: 0.8),
            ),
            backgroundColor: kWhite.withValues(alpha: 0.2),
          ),
        ),
        SizedBox(height: 16.h),
        Text(
          'Loading amazing photos...',
          style: FontPalette.urbenist12.copyWith(
            color: kWhite.withValues(alpha: 0.6),
            fontWeight: FontWeight.w300,
          ),
        ),
      ],
    );
  }
}

class ParticlesPainter extends CustomPainter {
  final double animationValue;
  final Random random = Random();

  ParticlesPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = kSecondaryColor.withValues(alpha: 0.1)
      ..style = PaintingStyle.fill;

    // Create floating particles
    for (int i = 0; i < 20; i++) {
      final x = (size.width * (i / 20)) +
          (sin(animationValue * 2 * pi + i) * 30);
      final y = (size.height * ((i * 0.7) % 1)) +
          (cos(animationValue * 2 * pi + i) * 20);

      final radius = 2 + (sin(animationValue * 4 * pi + i) * 1);

      canvas.drawCircle(
        Offset(x, y),
        radius,
        paint,
      );
    }

    // Create larger floating elements
    final largePaint = Paint()
      ..color = kSecondaryColor.withValues(alpha: 0.05)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 8; i++) {
      final x = (size.width * (i / 8)) +
          (sin(animationValue * pi + i * 0.5) * 50);
      final y = (size.height * ((i * 0.3) % 1)) +
          (cos(animationValue * pi + i * 0.5) * 30);

      final radius = 8 + (sin(animationValue * 3 * pi + i) * 4);

      canvas.drawCircle(
        Offset(x, y),
        radius,
        largePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
