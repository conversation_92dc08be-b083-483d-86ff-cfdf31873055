import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:pixs/features/onboarding/services/onboarding_service.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/dependency_injection/injectable.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/themes/font_palette.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  
  int _currentPage = 0;
  final int _totalPages = 4;

  final List<OnboardingData> _pages = [
    OnboardingData(
      title: 'Discover\nBeautiful Photos',
      subtitle: 'Explore millions of stunning high-quality images from talented photographers worldwide',
      imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=1200&fit=crop&q=80',
      primaryColor: const Color(0xFF6C5CE7),
      secondaryColor: const Color(0xFFA29BFE),
    ),
    OnboardingData(
      title: 'AI-Powered\nCreation',
      subtitle: 'Generate stunning custom images using advanced AI technology with simple text prompts',
      imageUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=800&h=1200&fit=crop&q=80',
      primaryColor: const Color(0xFFE17055),
      secondaryColor: const Color(0xFFFAB1A0),
    ),
    OnboardingData(
      title: 'Curated\nCollections',
      subtitle: 'Browse carefully organized photo collections for every mood, style, and project',
      imageUrl: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?w=800&h=1200&fit=crop&q=80',
      primaryColor: const Color(0xFF00B894),
      secondaryColor: const Color(0xFF55EFC4),
    ),
    OnboardingData(
      title: 'Save &\nWallpapers',
      subtitle: 'Save your favorites and set beautiful wallpapers directly from the app',
      imageUrl: 'https://images.unsplash.com/photo-1579952363873-27d3bfad9c0d?w=800&h=1200&fit=crop&q=80',
      primaryColor: const Color(0xFFE84393),
      secondaryColor: const Color(0xFFFD79A8),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 400),
        curve: Curves.easeInOutCubic,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  Future<void> _completeOnboarding() async {
    await getIt<OnboardingService>().markOnboardingCompleted();
    
    if (mounted) {
      Navigator.pushNamedAndRemoveUntil(
        context,
        routeMain,
        (route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: kBlack,
      body: Stack(
        children: [
          // Background Image with Gradient Overlay
          _buildBackgroundImage(),
          
          // Content
          SafeArea(
            child: Column(
              children: [
                // Top Bar
                _buildTopBar(),
                
                // Main Content
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    onPageChanged: (index) {
                      setState(() {
                        _currentPage = index;
                      });
                      _animationController.reset();
                      _animationController.forward();
                    },
                    itemCount: _totalPages,
                    itemBuilder: (context, index) {
                      return _buildPage(_pages[index]);
                    },
                  ),
                ),
                
                // Bottom Section
                _buildBottomSection(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBackgroundImage() {
    return AnimatedSwitcher(
      duration: const Duration(milliseconds: 600),
      child: Container(
        key: ValueKey(_currentPage),
        child: Stack(
          children: [
            // Background Image
            CachedNetworkImage(
              imageUrl: _pages[_currentPage].imageUrl,
              width: double.infinity,
              height: double.infinity,
              fit: BoxFit.cover,
              placeholder: (context, url) => Container(
                color: _pages[_currentPage].primaryColor.withValues(alpha: 0.3),
              ),
              errorWidget: (context, url, error) => Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      _pages[_currentPage].primaryColor,
                      _pages[_currentPage].secondaryColor,
                    ],
                  ),
                ),
              ),
            ),
            
            // Gradient Overlay
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    kBlack.withValues(alpha: 0.4),
                    kBlack.withValues(alpha: 0.7),
                    kBlack.withValues(alpha: 0.9),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: EdgeInsets.all(24.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Logo
          Text(
            'PIXS',
            style: FontPalette.urbenist24.copyWith(
              color: kWhite,
              fontWeight: FontWeight.bold,
              letterSpacing: 3.0,
            ),
          ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.3),
          
          // Skip Button
          TextButton(
            onPressed: _skipOnboarding,
            style: TextButton.styleFrom(
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
              backgroundColor: kWhite.withValues(alpha: 0.1),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.r),
              ),
            ),
            child: Text(
              'Skip',
              style: FontPalette.urbenist14.copyWith(
                color: kWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ).animate().fadeIn(duration: 600.ms, delay: 200.ms).slideX(begin: 0.3),
        ],
      ),
    );
  }

  Widget _buildPage(OnboardingData data) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 32.w),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 100.h),
          
          // Title
          Text(
            data.title,
            textAlign: TextAlign.center,
            style: FontPalette.urbenist42.copyWith(
              color: kWhite,
              fontWeight: FontWeight.bold,
              height: 1.1,
            ),
          ).animate(target: 1)
              .fadeIn(duration: 800.ms, delay: 200.ms)
              .slideY(begin: 0.3, duration: 800.ms),
          
          SizedBox(height: 24.h),
          
          // Subtitle
          Text(
            data.subtitle,
            textAlign: TextAlign.center,
            style: FontPalette.urbenist16.copyWith(
              color: kWhite.withValues(alpha: 0.9),
              fontWeight: FontWeight.w400,
              height: 1.5,
            ),
          ).animate(target: 1)
              .fadeIn(duration: 800.ms, delay: 400.ms)
              .slideY(begin: 0.3, duration: 800.ms),
          
          SizedBox(height: 60.h),
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    return Padding(
      padding: EdgeInsets.all(32.w),
      child: Column(
        children: [
          // Page Indicator
          _buildPageIndicator(),
          
          SizedBox(height: 40.h),
          
          // Action Button
          _buildActionButton(),
        ],
      ),
    );
  }

  Widget _buildPageIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(_totalPages, (index) {
        final isActive = index == _currentPage;
        return AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          margin: EdgeInsets.symmetric(horizontal: 4.w),
          width: isActive ? 32.w : 8.w,
          height: 8.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.r),
            color: isActive 
                ? _pages[_currentPage].primaryColor
                : kWhite.withValues(alpha: 0.3),
          ),
        );
      }),
    ).animate().fadeIn(duration: 600.ms, delay: 600.ms);
  }

  Widget _buildActionButton() {
    final isLastPage = _currentPage == _totalPages - 1;
    
    return Container(
      width: double.infinity,
      height: 56.h,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(28.r),
        gradient: LinearGradient(
          colors: [
            _pages[_currentPage].primaryColor,
            _pages[_currentPage].secondaryColor,
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: _pages[_currentPage].primaryColor.withValues(alpha: 0.4),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(28.r),
          onTap: _nextPage,
          child: Center(
            child: Text(
              isLastPage ? 'Get Started' : 'Next',
              style: FontPalette.urbenist18.copyWith(
                color: kWhite,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ),
      ),
    ).animate().fadeIn(duration: 600.ms, delay: 800.ms).slideY(begin: 0.3);
  }
}

class OnboardingData {
  final String title;
  final String subtitle;
  final String imageUrl;
  final Color primaryColor;
  final Color secondaryColor;

  OnboardingData({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    required this.primaryColor,
    required this.secondaryColor,
  });
}
