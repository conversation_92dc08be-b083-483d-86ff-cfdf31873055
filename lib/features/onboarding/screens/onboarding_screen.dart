import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:pixs/features/onboarding/services/onboarding_service.dart';
import 'package:pixs/features/onboarding/widgets/onboarding_page.dart';
import 'package:pixs/features/onboarding/widgets/page_indicator.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/dependency_injection/injectable.dart';
import 'package:pixs/shared/routes/routes.dart';
import 'package:pixs/shared/themes/font_palette.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({super.key});

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _backgroundController;
  late AnimationController _buttonController;
  
  int _currentPage = 0;
  final int _totalPages = 5;

  final List<OnboardingPageData> _pages = [
    OnboardingPageData(
      title: 'Discover Beautiful Photos',
      subtitle: 'Explore millions of high-quality images from talented photographers around the world',
      imageUrl: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=600&fit=crop',
      icon: Icons.explore,
      gradient: [Color(0xFF667eea), Color(0xFF764ba2)],
    ),
    OnboardingPageData(
      title: 'AI-Powered Creation',
      subtitle: 'Generate stunning custom images using advanced AI technology with simple text prompts',
      imageUrl: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=400&h=600&fit=crop',
      icon: Icons.auto_awesome,
      gradient: [Color(0xFFf093fb), Color(0xFFf5576c)],
    ),
    OnboardingPageData(
      title: 'Curated Collections',
      subtitle: 'Browse carefully organized photo collections for every mood, style, and project need',
      imageUrl: 'https://images.unsplash.com/photo-1618005182384-a83a8bd57fbe?w=400&h=600&fit=crop',
      icon: Icons.collections,
      gradient: [Color(0xFF4facfe), Color(0xFF00f2fe)],
    ),
    OnboardingPageData(
      title: 'Save & Organize',
      subtitle: 'Create your personal collection by saving favorite images and organizing them your way',
      imageUrl: 'https://images.unsplash.com/photo-1586953208448-b95a79798f07?w=400&h=600&fit=crop',
      icon: Icons.favorite,
      gradient: [Color(0xFFfa709a), Color(0xFFfee140)],
    ),
    OnboardingPageData(
      title: 'Set as Wallpaper',
      subtitle: 'Transform your device with beautiful wallpapers. Download and set images instantly',
      imageUrl: 'https://images.unsplash.com/photo-1579952363873-27d3bfad9c0d?w=400&h=600&fit=crop',
      icon: Icons.wallpaper,
      gradient: [Color(0xFFa8edea), Color(0xFFfed6e3)],
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
    
    _buttonController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _backgroundController.dispose();
    _buttonController.dispose();
    super.dispose();
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _completeOnboarding();
    }
  }

  void _skipOnboarding() {
    _completeOnboarding();
  }

  Future<void> _completeOnboarding() async {
    _buttonController.forward();
    
    // Mark onboarding as completed
    await getIt<OnboardingService>().markOnboardingCompleted();
    
    // Navigate to main screen
    if (mounted) {
      Navigator.pushNamedAndRemoveUntil(
        context,
        routeMain,
        (route) => false,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Animated background
          _buildAnimatedBackground(),
          
          // Main content
          SafeArea(
            child: Column(
              children: [
                // Skip button
                _buildTopBar(),
                
                // Page content
                Expanded(
                  child: PageView.builder(
                    controller: _pageController,
                    onPageChanged: (index) {
                      setState(() {
                        _currentPage = index;
                      });
                    },
                    itemCount: _totalPages,
                    itemBuilder: (context, index) {
                      return OnboardingPage(
                        data: _pages[index],
                        isActive: index == _currentPage,
                      );
                    },
                  ),
                ),
                
                // Bottom section with indicator and button
                _buildBottomSection(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnimatedBackground() {
    return AnimatedBuilder(
      animation: _backgroundController,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                kBlack,
                kBlack.withValues(alpha: 0.8),
                _pages[_currentPage].gradient[0].withValues(alpha: 0.1),
                _pages[_currentPage].gradient[1].withValues(alpha: 0.1),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildTopBar() {
    return Padding(
      padding: EdgeInsets.all(20.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Logo or app name
          Text(
            'PIXS',
            style: FontPalette.urbenist24.copyWith(
              color: kWhite,
              fontWeight: FontWeight.bold,
              letterSpacing: 2.0,
            ),
          ).animate().fadeIn(duration: 600.ms).slideX(begin: -0.3),
          
          // Skip button
          TextButton(
            onPressed: _skipOnboarding,
            child: Text(
              'Skip',
              style: FontPalette.urbenist16.copyWith(
                color: kWhite.withValues(alpha: 0.7),
                fontWeight: FontWeight.w500,
              ),
            ),
          ).animate().fadeIn(duration: 600.ms, delay: 200.ms).slideX(begin: 0.3),
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    return Padding(
      padding: EdgeInsets.all(20.w),
      child: Column(
        children: [
          // Page indicator
          PageIndicator(
            currentPage: _currentPage,
            totalPages: _totalPages,
            activeColor: _pages[_currentPage].gradient[0],
            inactiveColor: kWhite.withValues(alpha: 0.3),
          ).animate().fadeIn(duration: 600.ms, delay: 400.ms),
          
          SizedBox(height: 30.h),
          
          // Action button
          _buildActionButton(),
        ],
      ),
    );
  }

  Widget _buildActionButton() {
    final isLastPage = _currentPage == _totalPages - 1;
    
    return AnimatedBuilder(
      animation: _buttonController,
      builder: (context, child) {
        return Container(
          width: double.infinity,
          height: 56.h,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(28.r),
            gradient: LinearGradient(
              colors: _pages[_currentPage].gradient,
            ),
            boxShadow: [
              BoxShadow(
                color: _pages[_currentPage].gradient[0].withValues(alpha: 0.3),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              borderRadius: BorderRadius.circular(28.r),
              onTap: _nextPage,
              child: Center(
                child: Text(
                  isLastPage ? 'Get Started' : 'Next',
                  style: FontPalette.urbenist18.copyWith(
                    color: kWhite,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ).animate().fadeIn(duration: 600.ms, delay: 600.ms).slideY(begin: 0.3);
      },
    );
  }
}

class OnboardingPageData {
  final String title;
  final String subtitle;
  final String imageUrl;
  final IconData icon;
  final List<Color> gradient;

  OnboardingPageData({
    required this.title,
    required this.subtitle,
    required this.imageUrl,
    required this.icon,
    required this.gradient,
  });
}
