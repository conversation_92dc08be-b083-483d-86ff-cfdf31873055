import 'package:hive_flutter/hive_flutter.dart';
import 'package:injectable/injectable.dart';

@lazySingleton
class OnboardingService {
  static const String _onboardingBoxName = 'onboarding';
  static const String _isFirstTimeKey = 'is_first_time';
  
  Box? _onboardingBox;

  /// Initialize the onboarding service
  Future<void> initialize() async {
    try {
      _onboardingBox = await Hive.openBox(_onboardingBoxName);
    } catch (e) {
      // If box opening fails, create a new one
      _onboardingBox = await Hive.openBox(_onboardingBoxName);
    }
  }

  /// Check if this is the first time the user is opening the app
  bool get isFirstTime {
    try {
      if (_onboardingBox == null) {
        return true; // Default to first time if box is not initialized
      }
      return _onboardingBox!.get(_isFirstTimeKey, defaultValue: true) as bool;
    } catch (e) {
      return true; // Default to first time on error
    }
  }

  /// Mark that the user has completed onboarding
  Future<void> markOnboardingCompleted() async {
    try {
      if (_onboardingBox == null) {
        await initialize();
      }
      await _onboardingBox!.put(_isFirstTimeKey, false);
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Reset onboarding status (for testing purposes)
  Future<void> resetOnboarding() async {
    try {
      if (_onboardingBox == null) {
        await initialize();
      }
      await _onboardingBox!.put(_isFirstTimeKey, true);
    } catch (e) {
      // Silently fail - not critical
    }
  }

  /// Clear all onboarding data
  Future<void> clearOnboardingData() async {
    try {
      if (_onboardingBox == null) {
        await initialize();
      }
      await _onboardingBox!.clear();
    } catch (e) {
      // Silently fail - not critical
    }
  }
}
