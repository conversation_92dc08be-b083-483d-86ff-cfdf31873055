import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:pixs/features/onboarding/screens/onboarding_screen.dart';
import 'package:pixs/shared/constants/colors.dart';
import 'package:pixs/shared/themes/font_palette.dart';
import 'package:shimmer/shimmer.dart';

class OnboardingPage extends StatefulWidget {
  final OnboardingPageData data;
  final bool isActive;

  const OnboardingPage({
    super.key,
    required this.data,
    required this.isActive,
  });

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage>
    with TickerProviderStateMixin {
  late AnimationController _floatingController;
  late AnimationController _pulseController;

  @override
  void initState() {
    super.initState();
    _floatingController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat(reverse: true);

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _floatingController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Column(
        children: [
          SizedBox(height: 40.h),
          
          // Feature icon with animation
          _buildFeatureIcon(),
          
          SizedBox(height: 40.h),
          
          // Image showcase
          _buildImageShowcase(),
          
          SizedBox(height: 40.h),
          
          // Title and subtitle
          _buildTextContent(),
          
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  Widget _buildFeatureIcon() {
    return AnimatedBuilder(
      animation: _pulseController,
      builder: (context, child) {
        return Container(
          width: 80.w,
          height: 80.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: widget.data.gradient,
            ),
            boxShadow: [
              BoxShadow(
                color: widget.data.gradient[0].withValues(alpha: 0.3),
                blurRadius: 20 + (_pulseController.value * 10),
                spreadRadius: 2 + (_pulseController.value * 3),
              ),
            ],
          ),
          child: Icon(
            widget.data.icon,
            size: 40.sp,
            color: kWhite,
          ),
        );
      },
    ).animate(target: widget.isActive ? 1 : 0)
        .fadeIn(duration: 800.ms)
        .scale(begin: const Offset(0.5, 0.5), duration: 800.ms, curve: Curves.elasticOut);
  }

  Widget _buildImageShowcase() {
    return AnimatedBuilder(
      animation: _floatingController,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, _floatingController.value * 10),
          child: Container(
            width: 280.w,
            height: 400.h,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(24.r),
              boxShadow: [
                BoxShadow(
                  color: widget.data.gradient[0].withValues(alpha: 0.2),
                  blurRadius: 30,
                  offset: const Offset(0, 15),
                ),
                BoxShadow(
                  color: kBlack.withValues(alpha: 0.3),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(24.r),
              child: Stack(
                children: [
                  // Main image
                  CachedNetworkImage(
                    imageUrl: widget.data.imageUrl,
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => _buildImagePlaceholder(),
                    errorWidget: (context, url, error) => _buildImageError(),
                  ),
                  
                  // Gradient overlay
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          widget.data.gradient[0].withValues(alpha: 0.1),
                          widget.data.gradient[1].withValues(alpha: 0.2),
                        ],
                      ),
                    ),
                  ),
                  
                  // Decorative elements
                  _buildDecorativeElements(),
                ],
              ),
            ),
          ),
        );
      },
    ).animate(target: widget.isActive ? 1 : 0)
        .fadeIn(duration: 1000.ms, delay: 200.ms)
        .slideY(begin: 0.3, duration: 1000.ms, curve: Curves.easeOutCubic);
  }

  Widget _buildImagePlaceholder() {
    return Shimmer.fromColors(
      baseColor: kBlack.withValues(alpha: 0.3),
      highlightColor: kWhite.withValues(alpha: 0.1),
      child: Container(
        width: double.infinity,
        height: double.infinity,
        color: kBlack.withValues(alpha: 0.3),
      ),
    );
  }

  Widget _buildImageError() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: widget.data.gradient,
        ),
      ),
      child: Icon(
        widget.data.icon,
        size: 80.sp,
        color: kWhite.withValues(alpha: 0.7),
      ),
    );
  }

  Widget _buildDecorativeElements() {
    return Positioned.fill(
      child: CustomPaint(
        painter: DecorativePainter(
          color: kWhite.withValues(alpha: 0.1),
          animationValue: _floatingController.value,
        ),
      ),
    );
  }

  Widget _buildTextContent() {
    return Column(
      children: [
        // Title
        Text(
          widget.data.title,
          textAlign: TextAlign.center,
          style: FontPalette.urbenist30.copyWith(
            color: kWhite,
            fontWeight: FontWeight.bold,
            height: 1.2,
          ),
        ).animate(target: widget.isActive ? 1 : 0)
            .fadeIn(duration: 800.ms, delay: 400.ms)
            .slideY(begin: 0.3, duration: 800.ms),
        
        SizedBox(height: 16.h),
        
        // Subtitle
        Text(
          widget.data.subtitle,
          textAlign: TextAlign.center,
          style: FontPalette.urbenist16.copyWith(
            color: kWhite.withValues(alpha: 0.8),
            fontWeight: FontWeight.w400,
            height: 1.5,
          ),
        ).animate(target: widget.isActive ? 1 : 0)
            .fadeIn(duration: 800.ms, delay: 600.ms)
            .slideY(begin: 0.3, duration: 800.ms),
      ],
    );
  }
}

class DecorativePainter extends CustomPainter {
  final Color color;
  final double animationValue;

  DecorativePainter({
    required this.color,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Draw floating circles
    for (int i = 0; i < 5; i++) {
      final x = (size.width * (i / 5)) + (animationValue * 20);
      final y = (size.height * 0.2) + (animationValue * 15);
      final radius = 3 + (animationValue * 2);
      
      canvas.drawCircle(Offset(x, y), radius, paint);
    }

    // Draw floating lines
    final linePaint = Paint()
      ..color = color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    for (int i = 0; i < 3; i++) {
      final startX = size.width * 0.1;
      final startY = (size.height * 0.7) + (i * 20) + (animationValue * 10);
      final endX = size.width * 0.3;
      final endY = startY + 5;
      
      canvas.drawLine(Offset(startX, startY), Offset(endX, endY), linePaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
